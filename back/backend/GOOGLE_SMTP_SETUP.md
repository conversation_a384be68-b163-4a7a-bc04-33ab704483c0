# Google SMTP Setup Guide

## Overview
This guide explains how to configure Google SMTP for the forgot password OTP functionality in your backend application.

## Prerequisites
1. A Gmail account
2. Two-factor authentication enabled on your Gmail account

## Setup Steps

### 1. Enable 2-Factor Authentication
1. Go to your Google Account settings
2. Navigate to Security
3. Enable 2-Step Verification if not already enabled

### 2. Generate App Password
1. Go to Google Account settings > Security
2. Under "2-Step Verification", click on "App passwords"
3. Select "Mail" as the app and "Other" as the device
4. Enter a custom name like "Super Up Backend"
5. Google will generate a 16-character app password
6. Copy this password - you'll need it for the EMAIL_PASSWORD

### 3. Update Environment Variables
Update your `.env.development` and `.env.production` files with:

```env
# Google SMTP Configuration
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-16-character-app-password"
```

**Important Notes:**
- Replace `<EMAIL>` with your actual Gmail address
- Replace `your-16-character-app-password` with the app password generated in step 2
- Do NOT use your regular Gmail password - use the app password
- Keep EMAIL_SECURE=false for port 587 (STARTTLS)

### 4. Testing
1. Restart your backend server
2. Test the forgot password functionality from your Flutter app
3. Check that OTP emails are being sent successfully

## Troubleshooting

### Common Issues:
1. **Authentication failed**: Make sure you're using the app password, not your regular password
2. **Connection timeout**: Check your firewall settings and ensure port 587 is open
3. **Invalid credentials**: Verify the email address and app password are correct

### Security Notes:
- Never commit your actual credentials to version control
- Use environment variables for sensitive information
- Consider using different Gmail accounts for development and production

## Configuration Details

The backend uses the following configuration:
- **Host**: smtp.gmail.com
- **Port**: 587 (STARTTLS)
- **Security**: TLS (not SSL)
- **Authentication**: Username/Password (App Password)

This configuration is compatible with Google's SMTP requirements and provides secure email delivery for your OTP functionality.
