/**
 * Copyright 2023, the hate<PERSON>ragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { Transporter } from 'nodemailer';
import * as fs from 'fs';
import * as path from 'path';
import root from 'app-root-path';

@Injectable()
export class GoogleSmtpService {
    private readonly logger = new Logger(GoogleSmtpService.name);
    private transporter: Transporter;

    constructor(private configService: ConfigService) {
        this.createTransporter();
    }

    private createTransporter() {
        this.transporter = nodemailer.createTransport({
            host: this.configService.getOrThrow('EMAIL_HOST'),
            port: this.configService.get('EMAIL_PORT') ? parseInt(this.configService.get('EMAIL_PORT')) : 587,
            secure: this.configService.get('EMAIL_SECURE') === 'true', // true for 465, false for other ports
            auth: {
                user: this.configService.getOrThrow('EMAIL_USER'),
                pass: this.configService.getOrThrow('EMAIL_PASSWORD'),
            },
            tls: {
                rejectUnauthorized: false,
            },
        });

        // Verify connection configuration
        this.transporter.verify((error, success) => {
            if (error) {
                this.logger.error('SMTP connection error:', error);
            } else {
                this.logger.log('SMTP server is ready to take our messages');
            }
        });
    }

    async sendPasswordResetEmail(to: string, name: string, code: string, appName: string): Promise<void> {
        try {
            const template = this.loadTemplate('password_reset', { name, code, appName });
            
            const mailOptions = {
                from: `"No Reply" <${this.configService.getOrThrow('EMAIL_USER')}>`,
                to: to,
                subject: appName,
                html: template,
            };

            const result = await this.transporter.sendMail(mailOptions);
            this.logger.log(`Password reset email sent successfully to ${to}. Message ID: ${result.messageId}`);
        } catch (error) {
            this.logger.error(`Failed to send password reset email to ${to}:`, error);
            throw error;
        }
    }

    async sendConfirmationEmail(to: string, name: string, code: string, appName: string): Promise<void> {
        try {
            const template = this.loadTemplate('confirmation', { name, code, appName });
            
            const mailOptions = {
                from: `"No Reply" <${this.configService.getOrThrow('EMAIL_USER')}>`,
                to: to,
                subject: appName,
                html: template,
            };

            const result = await this.transporter.sendMail(mailOptions);
            this.logger.log(`Confirmation email sent successfully to ${to}. Message ID: ${result.messageId}`);
        } catch (error) {
            this.logger.error(`Failed to send confirmation email to ${to}:`, error);
            throw error;
        }
    }

    private loadTemplate(templateName: string, context: any): string {
        try {
            const templatePath = path.join(root.path, 'src', 'api', 'mail', 'templates', `${templateName}.hbs`);
            let template = fs.readFileSync(templatePath, 'utf8');
            
            // Simple template replacement (basic handlebars-like functionality)
            Object.keys(context).forEach(key => {
                const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
                template = template.replace(regex, context[key]);
            });
            
            return template;
        } catch (error) {
            this.logger.error(`Failed to load template ${templateName}:`, error);
            // Fallback to simple HTML template
            return this.getFallbackTemplate(templateName, context);
        }
    }

    private getFallbackTemplate(templateName: string, context: any): string {
        if (templateName === 'password_reset') {
            return `
                <p>Hey ${context.name},</p>
                <p>${context.appName} Password reset</p>
                <p>
                    <span style="color: blue">${context.code}</span>
                </p>
                <p>Do not share this code with anyone this code is only valid for 10 minutes.</p>
            `;
        } else {
            return `
                <p>Hey ${context.name},</p>
                <p>${context.appName} Verification</p>
                <p>
                    <span style="color: blue">${context.code}</span>
                </p>
                <p>Do not share this code with anyone this code is only valid for 10 minutes.</p>
            `;
        }
    }
}
