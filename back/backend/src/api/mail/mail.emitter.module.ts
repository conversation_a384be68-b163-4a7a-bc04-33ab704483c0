/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {Module} from '@nestjs/common';
import {MailEmitterService} from './mail.emitter.service';
import {ConfigModule} from "@nestjs/config";
import {MailEvent} from "./mail.event";
import {AppConfigService} from "../app_config/app_config.service";
import {AppConfigModule} from "../app_config/app_config.module";
import {GoogleSmtpService} from "./google-smtp.service";

@Module({
    providers: [MailEmitterService, MailEvent, GoogleSmtpService],
    imports: [
        ConfigModule,
        AppConfigModule
    ],
    exports: [MailEmitterService, GoogleSmtpService],
})
export class MailEmitterModule {
}
