/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {Injectable, Logger} from "@nestjs/common";
import {OnEvent} from "@nestjs/event-emitter";
import {SendMailEvent} from "../../core/utils/interfaceces";
import {MailType} from "../../core/utils/enums";
import {AppConfigService} from "../app_config/app_config.service";
import {GoogleSmtpService} from "./google-smtp.service";

@Injectable()
export class MailEvent {
    private readonly logger = new Logger(MailEvent.name);

    constructor(
        private googleSmtpService: GoogleSmtpService,
        private appConfig: AppConfigService,
    ) {
    }


    @OnEvent("send.mail")
    async handleOrderCreatedEvent(event: SendMailEvent) {
        let appConfig = await this.appConfig.getConfig();
        try {
            if (event.mailType == MailType.ResetPassword) {
                await this.googleSmtpService.sendPasswordResetEmail(
                    event.user.email,
                    event.user.fullName,
                    event.code,
                    appConfig.appName
                );
            } else {
                await this.googleSmtpService.sendConfirmationEmail(
                    event.user.email,
                    event.user.fullName,
                    event.code,
                    appConfig.appName
                );
            }
        } catch (e) {
            this.logger.error('Failed to send email:', e);
        }
    }
}